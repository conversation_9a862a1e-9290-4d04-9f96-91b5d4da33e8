import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service for preprocessing images to improve OCR accuracy
class ImagePreprocessingService {
  /// Preprocess image for optimal OCR results
  /// This includes auto-cropping, enhancement, and optimization
  Future<File> preprocessForOCR(File originalImage) async {
    try {
      debugPrint('Starting image preprocessing for OCR...');

      // Load the original image
      final Uint8List imageBytes = await originalImage.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Failed to decode image');
      }

      debugPrint('Original image size: ${image.width}x${image.height}');

      // Step 1: Auto-crop to detect ID card boundaries
      image = await _autoCropIDCard(image);

      // Step 2: Enhance image quality for OCR
      image = await _enhanceForOCR(image);

      // Step 3: Optimize resolution for text recognition
      image = await _optimizeResolution(image);

      // Save the processed image
      final File processedImage = await _saveProcessedImage(image);

      debugPrint('Image preprocessing completed: ${processedImage.path}');
      return processedImage;
    } catch (e) {
      debugPrint('Error in image preprocessing: $e');
      // Return original image if preprocessing fails
      return originalImage;
    }
  }

  /// Auto-crop image to focus on ID card area
  Future<img.Image> _autoCropIDCard(img.Image image) async {
    try {
      debugPrint('Auto-cropping ID card...');

      // Convert to grayscale for edge detection
      img.Image grayscale = img.grayscale(image);

      // Apply edge detection to find card boundaries
      img.Image edges = _detectEdges(grayscale);

      // Find the largest rectangular contour (likely the ID card)
      Rectangle? cardBounds = _findCardBounds(edges);

      if (cardBounds != null) {
        debugPrint('Card bounds detected: ${cardBounds.toString()}');

        // Add some padding around the detected card
        int padding = 20;
        int x = (cardBounds.x - padding).clamp(0, image.width);
        int y = (cardBounds.y - padding).clamp(0, image.height);
        int width = (cardBounds.width + 2 * padding).clamp(0, image.width - x);
        int height = (cardBounds.height + 2 * padding).clamp(
          0,
          image.height - y,
        );

        // Crop the image to the card area
        img.Image cropped = img.copyCrop(
          image,
          x: x,
          y: y,
          width: width,
          height: height,
        );
        debugPrint('Cropped to: ${cropped.width}x${cropped.height}');
        return cropped;
      } else {
        debugPrint('No card bounds detected, using center crop');
        // Fallback: crop to center 80% of the image
        return _centerCrop(image, 0.8);
      }
    } catch (e) {
      debugPrint('Error in auto-crop: $e');
      return _centerCrop(image, 0.8);
    }
  }

  /// Enhance image quality for better OCR
  Future<img.Image> _enhanceForOCR(img.Image image) async {
    debugPrint('Enhancing image for OCR...');

    // Convert to grayscale for better text recognition
    image = img.grayscale(image);

    // Increase contrast significantly for better text visibility
    image = img.contrast(image, contrast: 150);

    // Adjust brightness using gamma correction
    image = img.gamma(image, gamma: 1.1);

    // Apply noise reduction using a slight blur followed by sharpening
    image = img.gaussianBlur(image, radius: 1);

    // Sharpen the image for clearer text - stronger sharpening
    image = img.convolution(image, filter: [0, -1, 0, -1, 6, -1, 0, -1, 0]);

    // Apply additional contrast enhancement
    image = img.normalize(image, min: 0, max: 255);

    return image;
  }

  /// Optimize image resolution for text recognition
  Future<img.Image> _optimizeResolution(img.Image image) async {
    debugPrint('Optimizing resolution...');

    // Target resolution for optimal OCR (around 1200-1600px width)
    const int targetWidth = 1400;

    if (image.width < targetWidth) {
      // Upscale if too small
      double scale = targetWidth / image.width;
      int newHeight = (image.height * scale).round();
      image = img.copyResize(
        image,
        width: targetWidth,
        height: newHeight,
        interpolation: img.Interpolation.cubic,
      );
      debugPrint('Upscaled to: ${image.width}x${image.height}');
    } else if (image.width > targetWidth * 2) {
      // Downscale if too large
      double scale = targetWidth / image.width;
      int newHeight = (image.height * scale).round();
      image = img.copyResize(
        image,
        width: targetWidth,
        height: newHeight,
        interpolation: img.Interpolation.cubic,
      );
      debugPrint('Downscaled to: ${image.width}x${image.height}');
    }

    return image;
  }

  /// Simple edge detection using Sobel operator
  img.Image _detectEdges(img.Image image) {
    // Simple edge detection - can be enhanced with more sophisticated algorithms
    return img.sobel(image);
  }

  /// Find card boundaries in edge-detected image
  Rectangle? _findCardBounds(img.Image edges) {
    // Simplified card detection - looks for the largest rectangular area
    // This is a basic implementation and can be enhanced with more sophisticated algorithms

    int width = edges.width;
    int height = edges.height;

    // Look for a rectangular area in the center portion of the image
    int centerX = width ~/ 2;
    int centerY = height ~/ 2;

    // Estimate card dimensions (Cambodia ID card ratio is approximately 1.6:1)
    int estimatedCardWidth = (width * 0.7).round();
    int estimatedCardHeight = (estimatedCardWidth / 1.6).round();

    // Position the card in the center
    int x = centerX - estimatedCardWidth ~/ 2;
    int y = centerY - estimatedCardHeight ~/ 2;

    // Ensure bounds are within image
    x = x.clamp(0, width - estimatedCardWidth);
    y = y.clamp(0, height - estimatedCardHeight);

    return Rectangle(x, y, estimatedCardWidth, estimatedCardHeight);
  }

  /// Center crop image by given factor
  img.Image _centerCrop(img.Image image, double factor) {
    int newWidth = (image.width * factor).round();
    int newHeight = (image.height * factor).round();

    int x = (image.width - newWidth) ~/ 2;
    int y = (image.height - newHeight) ~/ 2;

    return img.copyCrop(image, x: x, y: y, width: newWidth, height: newHeight);
  }

  /// Save processed image to temporary file
  Future<File> _saveProcessedImage(img.Image image) async {
    final Directory tempDir = await getTemporaryDirectory();
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String filePath = path.join(tempDir.path, 'processed_$timestamp.jpg');

    // Encode as JPEG with high quality
    final Uint8List jpegBytes = Uint8List.fromList(
      img.encodeJpg(image, quality: 95),
    );

    final File processedFile = File(filePath);
    await processedFile.writeAsBytes(jpegBytes);

    return processedFile;
  }
}

/// Simple rectangle class for bounds
class Rectangle {
  final int x;
  final int y;
  final int width;
  final int height;

  Rectangle(this.x, this.y, this.width, this.height);

  @override
  String toString() =>
      'Rectangle(x: $x, y: $y, width: $width, height: $height)';
}
